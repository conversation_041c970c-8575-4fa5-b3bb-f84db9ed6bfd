// src/hooks/useSwipeNavigation.js
import { useEffect, useRef, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";

const useSwipeNavigation = (options = {}) => {
  const {
    threshold = 60, // Giảm ngưỡng để dễ kích hoạt hơn
    minVelocity = 0.2, // Giảm vận tốc tối thiểu để dễ kích hoạt hơn
    maxDragPercentage = 40, // Phần trăm tối đa của màn hình có thể kéo
    elasticity = 0.5, // <PERSON><PERSON> số đàn hồi (0-1)
    preventDefaultOnSwipe = true,
    targetElement = null, // Element sẽ được kéo (null = toàn bộ trang)
    transitionSpeed = 250, // Giảm tốc độ animation để nhanh hơn
    swipeDirection = "right"
  } = options;

  const navigate = useNavigate();
  const location = useLocation();

  // Sử dụng useRef thay vì useState để tránh re-render
  const touchDataRef = useRef({
    startX: null,
    startY: null,
    startTime: null,
    currentX: null,
    moving: false
  });

  // Lưu trạng thái các element cần animation
  const dragElementRef = useRef(null);
  const overlayRef = useRef(null);
  const windowWidth = useRef(window.innerWidth);
  const isAnimatingRef = useRef(false);
  const rafIdRef = useRef(null);

  // Áp dụng transform cho element với requestAnimationFrame
  const applyTransform = useCallback((element, translateX, opacity = 0) => {
    if (!element) return;

    // Hủy animation frame trước đó nếu có
    if (rafIdRef.current) {
      cancelAnimationFrame(rafIdRef.current);
    }

    // Sử dụng requestAnimationFrame để tối ưu hiệu suất
    rafIdRef.current = requestAnimationFrame(() => {
      element.style.transition = '';
      // Sử dụng transform3d để kích hoạt GPU acceleration
      element.style.transform = `translate3d(${translateX}px, 0, 0)`;

      // Cập nhật overlay cho hiệu ứng bóng đổ khi kéo
      if (overlayRef.current) {
        overlayRef.current.style.transition = '';
        overlayRef.current.style.opacity = opacity.toString();
        overlayRef.current.style.backgroundColor = `rgba(0, 0, 0, ${opacity})`;
      }
    });
  }, []);

  // Reset lại transform với hiệu ứng transition
  const resetTransform = useCallback((element) => {
    if (!element || !element.style) return;

    isAnimatingRef.current = true;

    // Sử dụng requestAnimationFrame để tối ưu hiệu suất
    requestAnimationFrame(() => {
      element.style.transition = `transform ${transitionSpeed}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
      element.style.transform = 'translate3d(0, 0, 0)';

      if (overlayRef.current) {
        overlayRef.current.style.transition = `opacity ${transitionSpeed}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
        overlayRef.current.style.opacity = '0';
      }

      // Đánh dấu kết thúc animation sau khi transition hoàn tất
      setTimeout(() => {
        isAnimatingRef.current = false;
      }, transitionSpeed);
    });
  }, [transitionSpeed]);

  // Xác định đường dẫn cần chuyển đến dựa trên đường dẫn hiện tại
  const getNavigationTarget = useCallback(() => {
    const currentPath = location.pathname;

    // Luồng điều hướng: StudentEdu => Exams => ExerciseListBySubject => ExerciseDetail => (AttemptResults hoặc Attempt)

    // Trường hợp 1: Đang ở trang AttemptResults hoặc Attempt
    if (currentPath.includes('/attempt/')) {
      // Luôn quay về trang ExerciseDetail
      const examId = location.state?.examId;
      if (examId) {
        return {
          path: `/exercises/${examId}`,
          state: location.state || {}
        };
      } else {
        // Fallback nếu không có examId
        return {
          path: '/exams',
          state: location.state || {}
        };
      }
    }

    // Trường hợp 2: Đang ở trang ExerciseDetail
    else if (currentPath.includes('/exercises/')) {
      // Lấy subjectId từ state
      const subjectId = location.state?.subjectId;

      if (subjectId) {
        // Quay về trang ExerciseListBySubject
        return {
          path: `/subjects/${subjectId}/exercises`,
          state: location.state || {}
        };
      } else {
        // Fallback nếu không có subjectId
        return {
          path: '/exams',
          state: location.state || {}
        };
      }
    }

    // Trường hợp 3: Đang ở trang ExerciseListBySubject
    else if (currentPath.includes('/subjects/') && currentPath.includes('/exercises')) {
      // Luôn quay về trang Exams
      return {
        path: '/exams',
        state: location.state || {}
      };
    }

    // Trường hợp 4: Đang ở trang Exams
    else if (currentPath === '/exams') {
      // Quay về trang StudentEdu hoặc TeacherEdu dựa trên vai trò
      const role = location.state?.userRole || 'student';
      const homeRoute = role === 'student' ? '/student' : '/teacher';
      return {
        path: homeRoute,
        state: location.state || {}
      };
    }

    // Trường hợp mặc định: Sử dụng previousPath nếu có
    else if (location.state?.previousPath) {
      return {
        path: location.state.previousPath,
        state: location.state.previousState || {}
      };
    } else {
      // Fallback: Quay về trang chủ
      const role = 'student'; // Mặc định là student
      const homeRoute = role === 'student' ? '/student' : '/teacher';
      return {
        path: homeRoute,
        state: {}
      };
    }
  }, [location]);

  // Hoàn thành chuyển trang với hiệu ứng
  const completeTransition = useCallback((element) => {
    if (!element || isAnimatingRef.current) return;

    isAnimatingRef.current = true;

    // Xác định đường dẫn cần chuyển đến
    const target = getNavigationTarget();
    console.log('SwipeNavigation: Navigating to:', target.path);

    // Sử dụng requestAnimationFrame để tối ưu hiệu suất
    requestAnimationFrame(() => {
      // Tạo hiệu ứng kéo hoàn toàn ra
      element.style.transition = `transform ${transitionSpeed}ms cubic-bezier(0.19, 1, 0.22, 1)`;
      element.style.transform = `translate3d(${windowWidth.current}px, 0, 0)`;

      if (overlayRef.current) {
        overlayRef.current.style.transition = `opacity ${transitionSpeed}ms cubic-bezier(0.19, 1, 0.22, 1)`;
        overlayRef.current.style.opacity = '0.3';
      }

      // Thực hiện chuyển trang ngay lập tức với replace: true để tránh thêm vào history stack
      navigate(target.path, {
        replace: true,
        state: target.state
      });

      // Reset transform sau khi chuyển trang
      setTimeout(() => {
        resetTransform(element);
      }, 50);
    });
  }, [navigate, getNavigationTarget, transitionSpeed, resetTransform]);

  // Cập nhật element cần theo dõi dựa trên targetElement hoặc mặc định là body
  useEffect(() => {
    const element = targetElement
      ? (typeof targetElement === 'string' ? document.querySelector(targetElement) : targetElement)
      : document.body;

    dragElementRef.current = element;

    // Tạo overlay element nếu chưa có
    if (!overlayRef.current) {
      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.backgroundColor = 'rgba(0, 0, 0, 0)';
      overlay.style.pointerEvents = 'none';
      overlay.style.transition = `opacity ${transitionSpeed}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
      overlay.style.opacity = '0';
      overlay.style.zIndex = '9999';
      overlay.style.willChange = 'opacity'; // Báo trước cho browser biết thuộc tính nào sẽ thay đổi
      document.body.appendChild(overlay);
      overlayRef.current = overlay;
    }

    // Cập nhật lại kích thước khi thay đổi màn hình
    const handleResize = () => {
      windowWidth.current = window.innerWidth;
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (overlayRef.current && document.body.contains(overlayRef.current)) {
        document.body.removeChild(overlayRef.current);
      }

      // Hủy animation frame nếu có
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
    };
  }, [targetElement, transitionSpeed]);

  // Xử lý touch events
  useEffect(() => {
    // Tối ưu hóa các hàm xử lý sự kiện bằng useCallback
    const handleTouchStart = (e) => {
      // Nếu đang trong quá trình animation, không xử lý touch mới
      if (isAnimatingRef.current) return;

      // Chỉ theo dõi vuốt từ cạnh trái của màn hình (khoảng 30px từ cạnh)
      const touchX = e.touches[0].clientX;
      const isEdgeTouch = swipeDirection === "right" ? touchX < 30 : true;

      if (isEdgeTouch) {
        touchDataRef.current = {
          startX: e.touches[0].clientX,
          startY: e.touches[0].clientY,
          currentX: e.touches[0].clientX,
          startTime: Date.now(),
          moving: false
        };
      }
    };

    const handleTouchMove = (e) => {
      // Nếu đang trong quá trình animation, không xử lý touch move
      if (isAnimatingRef.current) return;

      const { startX, startY } = touchDataRef.current;

      if (startX === null) return;

      const currentX = e.touches[0].clientX;
      const currentY = e.touches[0].clientY;
      const diffX = currentX - startX;
      const diffY = currentY - startY;

      // Chỉ xử lý vuốt ngang, không xử lý vuốt dọc
      // Giảm ngưỡng để dễ nhận diện vuốt ngang hơn
      const isHorizontalSwipe = Math.abs(diffX) > Math.abs(diffY) * 0.8;

      // Chỉ tiếp tục xử lý nếu là vuốt ngang và đúng hướng
      if (isHorizontalSwipe &&
        ((swipeDirection === "right" && diffX > 0) ||
          (swipeDirection === "left" && diffX < 0) ||
          swipeDirection === "both")) {

        // Ngăn chặn scroll
        if (preventDefaultOnSwipe) {
          e.preventDefault();
        }

        // Tính toán khoảng cách kéo với độ đàn hồi
        // Càng kéo xa, càng khó kéo hơn do hệ số elasticity
        const maxDragPixels = windowWidth.current * (maxDragPercentage / 100);
        let dragX = diffX;

        if (Math.abs(dragX) > maxDragPixels) {
          const extraDrag = Math.abs(dragX) - maxDragPixels;
          dragX = (dragX > 0 ? 1 : -1) * (maxDragPixels + extraDrag * elasticity);
        }

        // Áp dụng transform cho element với opacity tỉ lệ theo % kéo
        // Tăng opacity để hiệu ứng rõ ràng hơn
        const opacity = Math.min(0.4, Math.abs(dragX) / (windowWidth.current * 1.5));

        // Sử dụng will-change để báo trước cho browser biết sẽ có animation
        if (dragElementRef.current && !dragElementRef.current.style.willChange) {
          dragElementRef.current.style.willChange = 'transform';
        }

        applyTransform(dragElementRef.current, dragX, opacity);

        touchDataRef.current = {
          ...touchDataRef.current,
          currentX,
          moving: true
        };
      }
    };

    const handleTouchEnd = (e) => {
      // Nếu đang trong quá trình animation, không xử lý touch end
      if (isAnimatingRef.current) return;

      const { startX, startTime, moving } = touchDataRef.current;

      if (startX === null || !moving) {
        resetTransform(dragElementRef.current);
        return;
      }

      const endX = e.changedTouches[0].clientX;
      const diffX = endX - startX;
      const elapsed = Date.now() - startTime;
      const velocityX = Math.abs(diffX) / elapsed;

      // Xóa will-change để giải phóng tài nguyên
      if (dragElementRef.current) {
        dragElementRef.current.style.willChange = '';
      }

      // Logic xác định hoàn thành hay hủy bỏ vuốt
      // Giảm ngưỡng để dễ kích hoạt hơn
      if (((swipeDirection === "right" && diffX > 0) ||
        (swipeDirection === "left" && diffX < 0) ||
        swipeDirection === "both") &&
        (Math.abs(diffX) > threshold || velocityX > minVelocity)) {

        // Tốc độ vuốt đủ nhanh hoặc kéo đủ xa
        if (swipeDirection === "right" && diffX > 0) {
          // Thực hiện chuyển trang
          completeTransition(dragElementRef.current);
        } else {
          // Xử lý chuyển trang với hướng khác nếu cần
        }
      } else {
        // Không đủ điều kiện, reset lại với animation mượt hơn
        resetTransform(dragElementRef.current);
      }

      // Reset lại state
      touchDataRef.current = {
        startX: null,
        startY: null,
        currentX: null,
        startTime: null,
        moving: false
      };
    };

    // Sử dụng passive: true khi có thể để cải thiện hiệu suất
    document.addEventListener("touchstart", handleTouchStart, { passive: true });
    document.addEventListener("touchmove", handleTouchMove, { passive: !preventDefaultOnSwipe });
    document.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener("touchstart", handleTouchStart);
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
      resetTransform(dragElementRef.current);
    };
  }, [
    applyTransform,
    resetTransform,
    completeTransition,
    threshold,
    minVelocity,
    preventDefaultOnSwipe,
    swipeDirection,
    maxDragPercentage,
    elasticity
  ]);

  return null;
};

export default useSwipeNavigation;