// src/hooks/useApiCache.js
import { useState, useEffect, useRef, useCallback } from 'react';

// Cache lưu trữ dữ liệu API
const apiCache = new Map();
// Cache lưu trữ timestamp của dữ liệu
const apiTimestamps = new Map();
// Cache lưu trữ các promise đang pending
const pendingPromises = new Map();

// Thời gian cache mặc định (5 phút)
const DEFAULT_CACHE_TIME = 5 * 60 * 1000;

/**
 * Custom hook để gọi API với caching
 * @param {Function} apiFn - Hàm gọi API (promise)
 * @param {Array} deps - Dependencies để quyết định khi nào gọi lại API
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn
 * @returns {Object} - { data, loading, error, refetch }
 */
const useApiCache = (apiFn, deps = [], options = {}) => {
  const {
    cacheKey, // Key để lưu trong cache
    cacheTime = DEFAULT_CACHE_TIME, // Thời gian cache (ms)
    enabled = true, // Có tự động gọi API không
    onSuccess, // Callback khi thành công
    onError, // Callback khi lỗi
    staleTime = 0, // Thời gian dữ liệu được coi là "fresh"
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Lưu trữ các callback để tránh re-render
  const callbackRef = useRef({
    onSuccess,
    onError,
  });

  // Cập nhật callback khi chúng thay đổi
  useEffect(() => {
    callbackRef.current = {
      onSuccess,
      onError,
    };
  }, [onSuccess, onError]);

  // Tạo key duy nhất nếu không được cung cấp
  const effectiveKey = useRef(cacheKey || `api_${Math.random().toString(36).substring(2)}`);

  // Hàm gọi API và xử lý cache
  const fetchData = useCallback(async (skipCache = false) => {
    const key = effectiveKey.current;

    // Nếu đang có request đang chạy với cùng key, sử dụng lại promise đó
    if (pendingPromises.has(key) && !skipCache) {
      return pendingPromises.get(key);
    }

    // Kiểm tra cache nếu không skip
    if (!skipCache) {
      const cachedData = apiCache.get(key);
      const timestamp = apiTimestamps.get(key);

      // Nếu có dữ liệu trong cache và chưa hết hạn
      if (cachedData && timestamp) {
        const now = Date.now();
        const isStale = now - timestamp > staleTime;

        // Nếu dữ liệu còn fresh, trả về ngay
        if (!isStale) {
          setData(cachedData);
          setLoading(false);
          setError(null);
          return Promise.resolve(cachedData);
        }

        // Nếu dữ liệu stale nhưng chưa hết hạn cache, vẫn hiển thị
        // nhưng sẽ gọi API mới ở background
        if (now - timestamp < cacheTime) {
          setData(cachedData);
          setLoading(false);
        }
      }
    }

    // Bắt đầu gọi API
    setLoading(true);

    // Tạo promise và lưu vào pending
    const promise = apiFn()
      .then(response => {
        // Lưu vào cache
        apiCache.set(key, response);
        apiTimestamps.set(key, Date.now());

        // Cập nhật state
        setData(response);
        setLoading(false);
        setError(null);

        // Gọi callback thành công
        if (callbackRef.current.onSuccess) {
          callbackRef.current.onSuccess(response);
        }

        // Xóa khỏi pending
        pendingPromises.delete(key);

        return response;
      })
      .catch(err => {
        console.error(`API Error (${key}):`, err);

        // Cập nhật state
        setError(err);
        setLoading(false);

        // Gọi callback lỗi
        if (callbackRef.current.onError) {
          callbackRef.current.onError(err);
        }

        // Xóa khỏi pending
        pendingPromises.delete(key);

        throw err;
      });

    // Lưu promise vào pending
    pendingPromises.set(key, promise);

    return promise;
  }, [apiFn, cacheTime, staleTime]);

  // Gọi API khi dependencies thay đổi
  useEffect(() => {
    if (!enabled) {
      setLoading(false);
      return;
    }

    fetchData(false).catch(() => {
      // Lỗi đã được xử lý trong fetchData
    });
  }, [...deps, enabled]);

  // Hàm để gọi lại API (force refresh)
  const refetch = useCallback((options = {}) => {
    const { showLoading = true } = options;

    if (!showLoading) {
      // Nếu không muốn hiển thị loading, gọi API mà không cập nhật state loading
      return apiFn()
        .then(response => {
          // Lưu vào cache
          apiCache.set(effectiveKey.current, response);
          apiTimestamps.set(effectiveKey.current, Date.now());

          // Cập nhật state data nhưng không cập nhật loading
          setData(response);
          setError(null);

          return response;
        })
        .catch(err => {
          console.error(`API Error (${effectiveKey.current}):`, err);
          // Không cập nhật state error để tránh hiển thị lỗi
          return null;
        });
    }

    // Mặc định: gọi API và hiển thị loading
    return fetchData(true);
  }, [fetchData, apiFn]);

  // Xóa cache khi component unmount
  useEffect(() => {
    return () => {
      // Không xóa cache khi unmount để các component khác có thể sử dụng
      // apiCache.delete(effectiveKey.current);
      // apiTimestamps.delete(effectiveKey.current);
    };
  }, []);

  return { data, loading, error, refetch };
};

export default useApiCache;
