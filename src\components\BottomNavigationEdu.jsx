// src/components/BottomNavigation.jsx
import { useNavigate, useLocation } from 'zmp-ui';
import { parseJwt } from '@/utils/jwt';

const BottomNavigationEdu = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const getUserRole = () => {
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('BottomNavigation: No token found');
      return null;
    }
    const parsedToken = parseJwt(token);
    console.log('BottomNavigation: Parsed token:', parsedToken);
    return parsedToken?.user?.role || null;
  };

  const getActiveKey = () => {
    console.log('BottomNavigation: Current pathname:', location.pathname);
    if (location.pathname === '/profile') return 'profile';
    if (location.pathname === '/directories') return 'directory';
    if (location.pathname === '/student' || location.pathname === '/teacher') return 'home';
    return 'home';
  };

  const getHomeRoute = () => {
    const role = getUserRole();
    const isTeacher = role === 'teacher' || role === 'TEACHER';
    const route = role === 'student' ? '/student' : isTeacher ? '/teacher' : '/login';
    console.log('BottomNavigation: User role:', role, 'Home route:', route);
    return route;
  };

  const handleNavigate = (route) => {
    // Nếu đã ở trang đó rồi thì không làm gì cả
    if (location.pathname === route) {
      console.log('BottomNavigation: Already on route:', route);
      return;
    }

    // Lưu lại thông tin trang hiện tại để có thể quay lại
    console.log('BottomNavigation: Navigating to:', route);
    navigate(route, {
      animate: true, // Đảm bảo animation
      state: {
        previousPath: location.pathname,
        previousState: location.state
      }
    });
  };

  const activeKey = getActiveKey();

  return (
    <div style={{
      position: 'fixed',
      bottom: 0,
      left: 0,
      right: 0,
      zIndex: 1000,
      maxWidth: '480px',
      margin: '0 auto',
      width: '100%',
    }}>
      {/* Phần nhô lên ở giữa - CHỈ NHÔ LÊN 1/3 */}
      <div style={{
        position: 'absolute',
        top: '-20px', // Chỉ nhô lên 20px (1/3 chiều cao)
        left: '50%',
        transform: 'translateX(-50%)',
        width: '60px',
        height: '60px',
        borderRadius: '50%',
        backgroundColor: '#0068ff',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.2)',
        border: '4px solid white',
        zIndex: 1001,
        cursor: 'pointer',
      }} onClick={() => handleNavigate('/directories')}>
        <span style={{
          fontSize: '26px',
          color: 'white',
          opacity: activeKey === 'directory' ? 1 : 0.8,
        }}>
          📋
        </span>
      </div>

      {/* Bottom Navigation chính */}
      <div style={{
        display: 'flex',
        backgroundColor: '#0068ff',
        height: '60px',
        paddingBottom: 'env(safe-area-inset-bottom)',
        borderTopLeftRadius: '20px',
        borderTopRightRadius: '20px',
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.2)',
        overflow: 'visible',
        position: 'relative',
      }}>
        {/* Item bên trái - ĐÃ HẠ XUỐNG */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            color: activeKey === 'home' ? 'white' : 'rgba(255, 255, 255, 0.7)',
            fontWeight: activeKey === 'home' ? 'bold' : 'normal',
            transition: 'all 0.3s ease',
            height: '100%',
            paddingTop: '25px', // Thêm padding-top để hạ nội dung xuống
          }}
          onClick={() => handleNavigate(getHomeRoute())}
        >
          <span style={{ fontSize: '22px', marginBottom: '2px' }}>🏠</span>
          <span style={{ fontSize: '11px' }}>Trang chủ</span>
        </div>

        {/* Khoảng trống ở giữa */}
        <div style={{ flex: 1 }}></div>

        {/* Item bên phải - ĐÃ HẠ XUỐNG */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            color: activeKey === 'profile' ? 'white' : 'rgba(255, 255, 255, 0.7)',
            fontWeight: activeKey === 'profile' ? 'bold' : 'normal',
            transition: 'all 0.3s ease',
            height: '100%',
            paddingTop: '25px', // Thêm padding-top để hạ nội dung xuống
          }}
          onClick={() => handleNavigate('/profile')}
        >
          <span style={{ fontSize: '22px', marginBottom: '2px' }}>👤</span>
          <span style={{ fontSize: '11px' }}>Cá nhân</span>
        </div>
      </div>
    </div>
  );
};

export default BottomNavigationEdu;