/* Ẩn thanh scrollbar nhưng vẫn cho phép cuộn */

/* Cho Chrome, Safari và Opera */
::-webkit-scrollbar {
  display: none;
}

/* Cho Firefox */
html {
  scrollbar-width: none;
}

/* Cho IE và Edge */
body {
  -ms-overflow-style: none;
}

/* Đ<PERSON>m bảo tất cả các phần tử có thể cuộn vẫn có thể cuộn */
* {
  -webkit-overflow-scrolling: touch;
}

/* Đảm bảo body và html có thể cuộn */
html, body {
  overflow-y: auto;
  height: 100%;
}

/* Đảm bảo các container có thể cuộn */
.container, .page-container {
  overflow-y: auto;
}

/* Thêm padding bottom cho container để tránh nội dung bị che bởi bottom navigation */
.container {
  padding-bottom: 60px;
}

/* Tối ưu hóa cho thiết bị di động */
@media (max-width: 768px) {
  * {
    -webkit-tap-highlight-color: transparent; /* Loại bỏ hiệu ứng highlight khi tap */
  }

  input, textarea, button, select, a {
    -webkit-tap-highlight-color: rgba(0,0,0,0);
  }
}

/* Tùy chỉnh BottomNavigation */
.zaui-bottom-navigation-item {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

.zaui-bottom-navigation-item-icon {
  margin-bottom: 6px !important;
}

.zaui-bottom-navigation-item-label {
  font-size: 12px !important;
  margin-top: 4px !important;
}
