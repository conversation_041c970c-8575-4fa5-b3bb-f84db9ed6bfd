import { getSystemInfo } from 'zmp-sdk';
import { AnimationRoutes, App, Route, SnackbarProvider, ZMPRouter } from 'zmp-ui';
import { AppProps } from 'zmp-ui/app';
import { useEffect } from 'react';
import { useNavigate } from 'zmp-ui';
import useSwipeNavigation from '@/hooks/useSwipeNavigation';
import Login from '@/pages/Login';
import StudentEdu from '@/pages/StudentEdu';
import TeacherEdu from '@/pages/TeacherEdu';
import Profile from '@/pages/Profile';
import ScheduleEdu from '@/pages/ScheduleEdu';
import AllAnnouncements from '@/pages/AllAnnouncements'; // Thêm AllAnnouncements
import AllEvents from '@/pages/AllEvents'; // Thêm AllEvents
import AllNews from '@/pages/AllNews'; // Thêm AllNews
import NewsDetail from '@/pages/NewsDetail'; // Thêm NewsDetail
import { parseJwt } from '@/utils/jwt';
import { SchoolYearProvider } from '@/context/SchoolYearContext';
import { AuthProvider } from '@/context/AuthContext';
import AuthInitializer from '@/components/AuthInitializer';
import Grades from '@/pages/Grades';
import Exams from '@/pages/Exams';
import ExerciseDetail from '@/pages/ExerciseDetail';
import Attempt from '@/pages/Attempt';
import AttemptResults from '@/pages/AttemptResults';
import ExerciseListBySubject from '@/pages/ExerciseListBySubject';
import Attendance from '@/pages/Attendance';
import Directory from '@/pages/Directory';

const AuthRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    console.log('AuthRedirect: Token:', token);
    if (token) {
      const parsedToken = parseJwt(token);
      console.log('AuthRedirect: Parsed token:', parsedToken);
      if (parsedToken && parsedToken.exp * 1000 > Date.now()) {
        if (parsedToken.user.role === 'student') {
          navigate('/student', { replace: true });
        } else if (parsedToken.user.role === 'teacher' || parsedToken.user.role === 'TEACHER') {
          console.log('AuthRedirect: Teacher role detected:', parsedToken.user.role);
          navigate('/teacher', { replace: true });
        } else {
          console.log('AuthRedirect: Invalid role:', parsedToken.user.role, 'removing token');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login', { replace: true });
        }
      } else {
        console.log('AuthRedirect: Invalid or expired token, removing token');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        navigate('/login', { replace: true });
      }
    } else {
      console.log('AuthRedirect: No token, redirecting to /login');
      navigate('/login', { replace: true });
    }
  }, [navigate]);

  return null;
};

const SwipeWrapper = ({ children }) => {
  useSwipeNavigation();
  return <>{children}</>;
};

const Layout = () => {
  return (
    <App theme={getSystemInfo().zaloTheme as AppProps['theme']}>
      <SchoolYearProvider>
        <SnackbarProvider>
          <AuthProvider>
            <ZMPRouter>
              <SwipeWrapper>
                <AnimationRoutes>
                  <Route path="/" element={<AuthRedirect />} />
                  <Route path="/login" element={<Login />} />
                  <Route
                    path="/student"
                    element={
                      <AuthInitializer>
                        <StudentEdu />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/teacher"
                    element={
                      <AuthInitializer>
                        <TeacherEdu />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/profile"
                    element={
                      <AuthInitializer>
                        <Profile />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/schedule"
                    element={
                      <AuthInitializer>
                        <ScheduleEdu />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/announcements"
                    element={
                      <AuthInitializer>
                        <AllAnnouncements />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/all-events"
                    element={
                      <AuthInitializer>
                        <AllEvents />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/grades"
                    element={
                      <AuthInitializer>
                        <Grades />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/exams"
                    element={
                      <AuthInitializer>
                        <Exams />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/exercises/:examId"
                    element={
                      <AuthInitializer>
                        <ExerciseDetail />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/attempt/:attemptId"
                    element={
                      <AuthInitializer>
                        <Attempt />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/attempt/:attemptId/results"
                    element={
                      <AuthInitializer>
                        <AttemptResults />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/subjects/:subjectId/exercises"
                    element={
                      <AuthInitializer>
                        <ExerciseListBySubject />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/attendance"
                    element={
                      <AuthInitializer>
                        <Attendance />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/directories"
                    element={
                      <AuthInitializer>
                        <Directory />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/news"
                    element={
                      <AuthInitializer>
                        <AllNews />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/news/:id"
                    element={
                      <AuthInitializer>
                        <NewsDetail />
                      </AuthInitializer>
                    }
                  />
                </AnimationRoutes>
              </SwipeWrapper>
            </ZMPRouter>
          </AuthProvider>
        </SnackbarProvider>
      </SchoolYearProvider>
    </App>
  );
};

export default Layout;