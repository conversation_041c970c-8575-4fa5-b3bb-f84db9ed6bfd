import React, { useContext, useEffect } from 'react';
import { useNavigate } from 'zmp-ui';
import { AuthContext } from '../context/AuthContext';

const AuthInitializer = ({ children }) => {
    const navigate = useNavigate();
    const { fetchUser, loading, user } = useContext(AuthContext);

    // Kiểm tra user khi component mount
    useEffect(() => {
        console.log('AuthInitializer: Checking user, loading:', loading, 'user:', user);
        if (!user && !loading) {
            console.log('AuthInitializer: Calling fetchUser');
            fetchUser();
        }
    }, [user, loading, fetchUser]);

    // Redirect nếu chưa đăng nhập
    useEffect(() => {
        console.log('AuthInitializer: Redirect check, loading:', loading, 'user:', user);
        if (!loading && !user && window.location.pathname !== '/login') {
            console.log('AuthInitializer: No user, redirecting to /login');
            navigate('/login', { replace: true });
        }
    }, [loading, user, navigate]);

    return <>{children}</>;
};

export default AuthInitializer;