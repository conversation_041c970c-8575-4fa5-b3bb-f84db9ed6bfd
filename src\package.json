{"name": "tunghia1-zalo-mini-app", "private": true, "version": "1.0.0", "description": "Trường THPT Số 1 Tư Nghĩa", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"start": "zmp start", "deploy": "zmp deploy", "build": "zmp build", "build:css": "postcss src/css/tailwind.css -o src/css/styles.css", "start:zalo": "zmp start --zalo-app --ios"}, "dependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.3.3", "clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^6.14.1", "react-router-dom": "^6.8.2", "zmp-sdk": "^2.43.4", "zmp-ui": "^1.11.5"}, "devDependencies": {"@types/node": "^18.14.6", "@vitejs/plugin-react": "^1.3", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "postcss-cli": "^10.1.0", "postcss-preset-env": "^6.7.0", "sass": "^1.58.3", "tailwindcss": "^3.2.7", "typescript": "^5.1.6", "vite": "2.9.15"}}