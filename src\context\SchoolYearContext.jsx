// src/context/SchoolYearContext.jsx
import React, { createContext, useContext } from 'react';

const SchoolYearContext = createContext();

export const SchoolYearProvider = ({ children }) => {
  const getSchoolYear = () => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1;
    if (currentMonth >= 8) {
      return `${currentYear}-${currentYear + 1}`;
    }
    return `${currentYear - 1}-${currentYear}`;
  };

  return (
    <SchoolYearContext.Provider value={{ schoolYear: getSchoolYear() }}>
      {children}
    </SchoolYearContext.Provider>
  );
};

export const useSchoolYear = () => useContext(SchoolYearContext);