/* src/styles/global.css */

/* Reset CSS */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  position: fixed;
  width: 100%;
  overflow: hidden;
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
}

/* Ngăn bouncing effect cho webview */
html {
  position: fixed;
  overflow: hidden;
}

body {
  position: fixed;
  overflow: hidden;
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
  touch-action: manipulation;
}

/* <PERSON><PERSON> hiệu h<PERSON>a scroll trên zaui-page */
.zaui-page {
  overflow: hidden !important;
  overscroll-behavior: none !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* Page */
.page {
  padding: 12px; /* Gi<PERSON>m padding từ 16px xuống 12px */
  height: 100%;
  overscroll-behavior: none;
}

/* Section container */
.section-container {
  padding: 12px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 12px; /* <PERSON><PERSON><PERSON><PERSON> từ 16px xuống 12px */
}

/* List và ListItem */
.zaui-list {
  overflow: hidden !important;
  margin: 0 !important;
}

.zaui-list-item {
  cursor: pointer;
}

/* Bottom navigation */
.btm-nav {
  padding-bottom: 0 !important;
  min-height: 56px !important;
  height: 56px !important;
  position: fixed;
  bottom: 0;
  width: 100%;
}

/* Safe area cho Zalo Mini App */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .page {
    padding-bottom: calc(12px + env(safe-area-inset-bottom));
  }
  .btm-nav {
    padding-bottom: env(safe-area-inset-bottom) !important;
  }
}

/* CSS cho nội dung tin tức (giữ nguyên) */
.news-content img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 10px 0;
}

.news-content h1,
.news-content h2,
.news-content h3,
.news-content h4,
.news-content h5,
.news-content h6 {
  color: #1f2937;
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 10px;
}

.news-content h1 { font-size: 24px; }
.news-content h2 { font-size: 22px; }
.news-content h3 { font-size: 20px; }
.news-content h4 { font-size: 18px; }
.news-content h5 { font-size: 16px; }
.news-content h6 { font-size: 14px; }

.news-content p {
  margin-bottom: 16px;
  line-height: 1.6;
  color: #4b5563;
}

.news-content a {
  color: #047857;
  text-decoration: underline;
}

.news-content ul,
.news-content ol {
  margin-left: 20px;
  margin-bottom: 16px;
}

.news-content li {
  margin-bottom: 8px;
}

.news-content figure {
  margin: 20px 0;
}

.news-content figure img {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.news-content figure figcaption {
  text-align: center;
  font-style: italic;
  color: #6b7280;
  margin-top: 6px;
  font-size: 14px;
}
