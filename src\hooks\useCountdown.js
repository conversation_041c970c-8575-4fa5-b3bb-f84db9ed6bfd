// src/hooks/useCountdown.js
import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook để quản lý đếm ngược thời gian
 * @param {number} initialTime - Thời gian ban đầu (tính bằng giây)
 * @param {Function} onComplete - Hàm callback khi đếm ngược kết thúc
 * @param {boolean} autoStart - Có tự động bắt đầu đếm ngược không
 * @returns {Object} - { remainingTime, isRunning, start, pause, reset }
 */
const useCountdown = (initialTime, onComplete, autoStart = true) => {
  const [remainingTime, setRemainingTime] = useState(initialTime);
  const [isRunning, setIsRunning] = useState(autoStart);
  const timerRef = useRef(null);
  const onCompleteRef = useRef(onComplete);

  // Cập nhật ref khi callback thay đổi
  useEffect(() => {
    onCompleteRef.current = onComplete;
  }, [onComplete]);

  // Hàm xử lý đếm ngược
  const tick = useCallback(() => {
    setRemainingTime((prevTime) => {
      if (prevTime <= 1) {
        clearInterval(timerRef.current);
        setIsRunning(false);
        if (onCompleteRef.current) {
          onCompleteRef.current();
        }
        return 0;
      }
      return prevTime - 1;
    });
  }, []);

  // Bắt đầu đếm ngược
  const start = useCallback(() => {
    if (isRunning || remainingTime <= 0) return;
    
    setIsRunning(true);
    timerRef.current = setInterval(tick, 1000);
  }, [isRunning, remainingTime, tick]);

  // Tạm dừng đếm ngược
  const pause = useCallback(() => {
    if (!isRunning) return;
    
    clearInterval(timerRef.current);
    setIsRunning(false);
  }, [isRunning]);

  // Reset đếm ngược
  const reset = useCallback((newTime = initialTime) => {
    clearInterval(timerRef.current);
    setRemainingTime(newTime);
    setIsRunning(false);
  }, [initialTime]);

  // Thiết lập timer khi component mount hoặc isRunning thay đổi
  useEffect(() => {
    if (isRunning && remainingTime > 0) {
      timerRef.current = setInterval(tick, 1000);
    } else if (!isRunning && timerRef.current) {
      clearInterval(timerRef.current);
    }
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRunning, tick, remainingTime]);

  // Cập nhật thời gian ban đầu nếu initialTime thay đổi
  useEffect(() => {
    if (!isRunning && initialTime !== remainingTime) {
      setRemainingTime(initialTime);
    }
  }, [initialTime, isRunning, remainingTime]);

  return {
    remainingTime,
    isRunning,
    start,
    pause,
    reset,
    // Hàm tiện ích để format thời gian
    formattedTime: formatTime(remainingTime),
  };
};

// Hàm format thời gian từ giây sang MM:SS
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
};

export default useCountdown;
