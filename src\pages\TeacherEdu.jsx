import React, { useEffect, useState, useRef, useContext, useCallback } from 'react';
import { Box, Text, List, Button, useNavigate, Modal, Select, Input, Checkbox } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import NewsComponent from '../components/NewsComponent';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';

const { Option } = Select;

const TeacherEdu = () => {
    const navigate = useNavigate();
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [classesLoading, setClassesLoading] = useState(true);
    const [tasksLoading, setTasksLoading] = useState(true);
    const [eventsLoading, setEventsLoading] = useState(false);
    const [notificationsLoading, setNotificationsLoading] = useState(true);
    const [classes, setClasses] = useState([]);
    const [tasks, setTasks] = useState([]);
    const [notifications, setNotifications] = useState([]);
    const [events, setEvents] = useState([]);
    const [eventPage, setEventPage] = useState(1);
    const [totalEventPages, setTotalEventPages] = useState(1);

    // Trạng thái cho modal
    const [modalVisible, setModalVisible] = useState(false);
    const [formData, setFormData] = useState({
        type: 'teacher_to_student',
        content: '',
        recipients: { classId: '', department: '' },
        zaloConfig: { enabled: false, groupId: '', groupName: '', scheduledTime: '' },
    });
    const [error, setError] = useState('');
    const [submitting, setSubmitting] = useState(false);

    // API data states
    const [availableClasses, setAvailableClasses] = useState([]);
    const [announcementConfig, setAnnouncementConfig] = useState(null);
    const [zaloGroups, setZaloGroups] = useState([]);
    const [loadingClasses, setLoadingClasses] = useState(false);
    const [loadingConfig, setLoadingConfig] = useState(false);
    const [loadingZaloGroups, setLoadingZaloGroups] = useState(false);

    // Khai báo useRef cho events
    const eventsContainerRef = useRef(null);
    const observerRef = useRef(null);
    const hasLoadedInitialEvents = useRef(false);
    const isLoadingMore = useRef(false);
    const loadedEventIds = useRef(new Set());

    // API functions
    const fetchClasses = useCallback(async () => {
        if (!user) return;
        setLoadingClasses(true);
        try {
            const response = await authApi.get('/directory/classes?schoolYear=2024-2025');
            console.log('Classes response:', response.data);
            if (response.data.success) {
                setAvailableClasses(response.data.data || []);
            }
        } catch (err) {
            console.error('Error fetching classes:', err);
            setError('Lỗi khi tải danh sách lớp học');
        } finally {
            setLoadingClasses(false);
        }
    }, [user]);

    const fetchAnnouncementConfig = useCallback(async () => {
        setLoadingConfig(true);
        try {
            const response = await authApi.get('/announcements/config');
            console.log('Announcement config response:', response.data);
            setAnnouncementConfig(response.data);
        } catch (err) {
            console.error('Error fetching announcement config:', err);
            setError('Lỗi khi tải cấu hình thông báo');
        } finally {
            setLoadingConfig(false);
        }
    }, []);

    const fetchZaloGroups = useCallback(async () => {
        if (!user) return;
        setLoadingZaloGroups(true);
        try {
            let response;
            if (user.role === 'admin') {
                response = await authApi.get('/zalo/groups?count=20&offset=0');
                console.log('Admin Zalo groups response:', response.data);
                setZaloGroups(response.data.data?.groups || []);
            } else {
                response = await authApi.get('/zalo/user/groups');
                console.log('User Zalo groups response:', response.data);
                setZaloGroups(response.data.user?.groupZaloIds || []);
            }
        } catch (err) {
            console.error('Error fetching Zalo groups:', err);
            setError('Lỗi khi tải danh sách nhóm Zalo');
        } finally {
            setLoadingZaloGroups(false);
        }
    }, [user]);

    // Kiểm tra xác thực
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && user.role !== 'teacher' && user.role !== 'TEACHER') {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Load initial data when modal opens
    useEffect(() => {
        if (modalVisible && user) {
            fetchAnnouncementConfig();
        }
    }, [modalVisible, user, fetchAnnouncementConfig]);

    // Load classes when type changes to teacher_to_student
    useEffect(() => {
        if (modalVisible && user && formData.type === 'teacher_to_student') {
            fetchClasses();
        }
    }, [modalVisible, user, formData.type, fetchClasses]);

    // Load Zalo groups when Zalo is enabled
    useEffect(() => {
        if (formData.zaloConfig.enabled && user) {
            fetchZaloGroups();
        }
    }, [formData.zaloConfig.enabled, user, fetchZaloGroups]);

    // Giả lập dữ liệu
    useEffect(() => {
        setClasses([
            { _id: '10A1', name: '10A1 - Toán học', students: 40, periods: 'Tiết 1-2', attendance: '38/40', progress: '95%' },
            { _id: '11A2', name: '11A2 - Toán học', students: 42, periods: 'Tiết 3-4', attendance: '40/42', progress: '95%' },
            { _id: '12A3', name: '12A3 - Đại số', students: 38, periods: 'Tiết 5-6', attendance: '36/38', progress: '95%' },
            { _id: '12A1', name: '12A1 - Hình học', students: 45, periods: 'Tiết 7-8', attendance: '43/45', progress: '96%' },
        ]);
        setClassesLoading(false);
    }, []);

    useEffect(() => {
        setTasks([
            { title: 'Bài tập cần chấm', meta: '10A1 - Hạn: Hôm nay', count: 32 },
            { title: 'Cập nhật điểm giữa kỳ', meta: '12A3 - Hạn: 25/04', count: 38 },
            { title: 'Tin nhắn phụ huynh', meta: 'Cần phản hồi', count: 5 },
        ]);
        setTasksLoading(false);
    }, []);

    useEffect(() => {
        setNotifications([
            { icon: '👨‍🎓', text: '<b>Nguyễn Văn An</b> đã nộp bài tập Toán học Chương 3', time: '10 phút trước' },
            { icon: '👩‍🎓', text: '<b>Trần Thị Bình</b> đã gửi câu hỏi về bài học', time: '30 phút trước' },
            { icon: '👨‍👩‍👧‍👦', text: '<b>Phụ huynh Lê Văn Cường</b> đã đặt lịch gặp mặt', time: '2 giờ trước' },
        ]);
        setNotificationsLoading(false);
    }, []);

    // Xử lý thay đổi form
    const handleFormChange = (field, value) => {
        setFormData((prev) => {
            let updatedFormData;
            switch (field) {
                case 'type':
                    updatedFormData = {
                        ...prev,
                        type: value,
                        recipients: { classId: '', department: '' },
                        zaloConfig: { ...prev.zaloConfig, groupId: '', groupName: '' },
                    };
                    break;
                case 'classId':
                    updatedFormData = {
                        ...prev,
                        recipients: { ...prev.recipients, classId: value },
                        zaloConfig: { ...prev.zaloConfig, groupId: '', groupName: '' },
                    };
                    break;
                case 'department':
                    updatedFormData = {
                        ...prev,
                        recipients: { ...prev.recipients, department: value },
                    };
                    break;
                case 'zaloEnabled':
                    updatedFormData = {
                        ...prev,
                        zaloConfig: { ...prev.zaloConfig, enabled: value, groupId: '', groupName: '', scheduledTime: '' },
                    };
                    break;
                case 'scheduledTime':
                    updatedFormData = {
                        ...prev,
                        zaloConfig: { ...prev.zaloConfig, scheduledTime: value },
                    };
                    break;
                case 'groupId':
                    const selectedGroup = zaloGroups.find(group =>
                        (user.role === 'admin' ? group.group_id : group.groupId) === value
                    );
                    updatedFormData = {
                        ...prev,
                        zaloConfig: {
                            ...prev.zaloConfig,
                            groupId: value,
                            groupName: selectedGroup ? (user.role === 'admin' ? selectedGroup.name : selectedGroup.groupName) : ''
                        },
                    };
                    break;
                case 'content':
                    updatedFormData = { ...prev, content: value };
                    break;
                default:
                    updatedFormData = prev;
            }
            return updatedFormData;
        });
        setError('');
    };

    // Xử lý gửi thông báo
    const handleSubmit = async () => {
        // Validation
        if (!formData.content) {
            setError('Vui lòng nhập nội dung thông báo');
            return;
        }

        // Validate based on announcement type
        if (formData.type === 'teacher_to_student' && !formData.recipients.classId) {
            setError('Vui lòng chọn lớp học để gửi thông báo cho học sinh');
            return;
        }

        if (formData.type === 'head_to_teacher' && !formData.recipients.department) {
            setError('Vui lòng chọn bộ môn để gửi thông báo cho giáo viên');
            return;
        }

        if (formData.zaloConfig.enabled && !formData.zaloConfig.groupId) {
            setError('Vui lòng chọn nhóm Zalo');
            return;
        }

        if (formData.zaloConfig.enabled && formData.zaloConfig.scheduledTime && new Date(formData.zaloConfig.scheduledTime) < new Date()) {
            setError('Thời gian lên lịch phải là trong tương lai');
            return;
        }

        setSubmitting(true);
        setError('');

        try {
            // Prepare request body
            const requestBody = {
                type: formData.type,
                content: formData.content,
                recipients: {},
                status: formData.zaloConfig.enabled && formData.zaloConfig.scheduledTime ? 'scheduled' : 'sent'
            };

            // Set recipients based on type
            switch (formData.type) {
                case 'teacher_to_student':
                    requestBody.recipients = {
                        classId: formData.recipients.classId,
                        schoolWide: false
                    };
                    break;
                case 'principal_to_teacher':
                case 'admin_to_all':
                    requestBody.recipients = {
                        schoolWide: true,
                        teachers: [],
                        department: null
                    };
                    break;
                case 'head_to_teacher':
                    requestBody.recipients = {
                        schoolWide: false,
                        teachers: [],
                        department: formData.recipients.department
                    };
                    break;
            }

            // Add Zalo config if enabled
            if (formData.zaloConfig.enabled) {
                requestBody.zaloConfig = {
                    enabled: true,
                    groupId: formData.zaloConfig.groupId,
                    groupName: formData.zaloConfig.groupName,
                    scheduledTime: formData.zaloConfig.scheduledTime || null,
                    sent: false
                };
            }

            await authApi.post('/announcements', requestBody);

            // Success
            setModalVisible(false);
            setFormData({
                type: 'teacher_to_student',
                content: '',
                recipients: { classId: '', department: '' },
                zaloConfig: { enabled: false, groupId: '', groupName: '', scheduledTime: '' },
            });

            // Update notifications
            const typeLabel = announcementConfig?.typeLabels[formData.type] || 'Thông báo';
            const actionText = formData.zaloConfig.scheduledTime ? 'đã được lên lịch' : 'đã được gửi';

            setNotifications((prev) => [
                {
                    icon: '📢',
                    text: `<b>${typeLabel}</b> ${actionText} thành công: ${formData.content.slice(0, 50)}${formData.content.length > 50 ? '...' : ''}`,
                    time: 'Vừa xong',
                },
                ...prev.slice(0, 2),
            ]);

        } catch (err) {
            console.error('Error sending announcement:', err);
            const errorMsg = err.response?.data?.msg || 'Có lỗi xảy ra khi gửi thông báo';
            setError(errorMsg);
        } finally {
            setSubmitting(false);
        }
    };

    // Giả lập dữ liệu sự kiện
    const fetchEvents = useCallback(() => {
        setEvents([
            { _id: '1', title: 'Buổi họp lớp 10A1', date: new Date() },
            { _id: '2', title: 'Kiểm tra Toán 11A2', date: new Date() },
        ]);
        setTotalEventPages(1);
        setEventsLoading(false);
    }, []);

    // Tải sự kiện ban đầu
    useEffect(() => {
        if (user && !hasLoadedInitialEvents.current) {
            fetchEvents();
            hasLoadedInitialEvents.current = true;
        }
    }, [user, fetchEvents]);

    // Xử lý scroll và IntersectionObserver
    const handleScroll = useCallback(() => {
        if (isLoadingMore.current || eventPage >= totalEventPages) return;

        const container = eventsContainerRef.current;
        if (!container) return;

        const scrollPosition = container.scrollLeft;
        const containerWidth = container.clientWidth;
        const scrollWidth = container.scrollWidth;

        const isNearEnd = scrollWidth - (scrollPosition + containerWidth) < 100;

        if (isNearEnd && !isLoadingMore.current && eventPage < totalEventPages) {
            setEventPage((prev) => prev + 1);
        }
    }, [eventPage, totalEventPages]);

    useEffect(() => {
        if (!observerRef.current || isLoadingMore.current || eventPage >= totalEventPages) return;

        const observer = new IntersectionObserver(
            (entries) => {
                const entry = entries[0];
                if (entry.isIntersecting && !isLoadingMore.current && eventPage < totalEventPages) {
                    setEventPage((prev) => prev + 1);
                }
            },
            { threshold: 0.5 }
        );

        const currentRef = observerRef.current;
        if (currentRef) {
            observer.observe(currentRef);
        }

        return () => {
            if (currentRef) {
                observer.unobserve(currentRef);
            }
            observer.disconnect();
        };
    }, [eventPage, totalEventPages]);

    useEffect(() => {
        const container = eventsContainerRef.current;
        if (!container || eventPage >= totalEventPages) return;

        let isThrottled = false;
        const throttledScroll = () => {
            if (!isThrottled) {
                handleScroll();
                isThrottled = true;
                setTimeout(() => {
                    isThrottled = false;
                }, 300);
            }
        };

        container.addEventListener('scroll', throttledScroll);
        return () => container.removeEventListener('scroll', throttledScroll);
    }, [handleScroll, eventPage, totalEventPages]);

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu />
            <HeaderSpacer />
            {/* Quick Access */}
            <Box className="quick-access" style={{ padding: '15px', display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '15px', backgroundColor: 'white' }}>
                {[
                    { icon: '✓', text: 'Điểm danh', path: '/teacher-attendance' },
                    { icon: '📝', text: 'Giao bài', path: '/teacher-assignments' },
                    { icon: '📊', text: 'Chấm bài', path: '/teacher-grading' },
                    { icon: '📅', text: 'Lịch dạy', path: '/teacher-schedule' },
                ].map((item, index) => (
                    <Box key={index} className="quick-item" flex flexDirection="column" alignItems="center" style={{ textAlign: 'center', cursor: 'pointer' }} onClick={() => navigate(item.path)}>
                        <Box className="quick-icon" style={{ width: '50px', height: '50px', borderRadius: '12px', backgroundColor: '#e8f0fe', display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '8px', color: '#0068ff', fontSize: '22px' }}>
                            {item.icon}
                        </Box>
                        <Text className="quick-text" style={{ fontSize: '12px', color: '#666' }}>{item.text}</Text>
                    </Box>
                ))}
            </Box>
            {/* Classes */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Lớp học của tôi</Text>
                    <Text className="see-all" style={{ color: '#0068ff', fontSize: '14px' }}>Xem tất cả</Text>
                </Box>
                <Box className="classes-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
                    {classes.map((item, index) => (
                        <Box key={index} className="class-card" style={{ backgroundColor: '#f0f6ff', borderRadius: '10px', padding: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                            <Text className="class-name" bold size="large">{item.name}</Text>
                            <Box className="class-details" flex justifyContent="space-between" style={{ fontSize: '12px', color: '#666', marginBottom: '10px' }}>
                                <Text>{item.students} học sinh</Text>
                                <Text>{item.periods}</Text>
                            </Box>
                            <Text>Điểm danh: {item.attendance}</Text>
                            <Box className="attendance-bar" style={{ height: '6px', backgroundColor: '#e0e0e0', borderRadius: '3px', marginTop: '8px', position: 'relative' }}>
                                <Box className="attendance-progress" style={{ width: item.progress, height: '100%', backgroundColor: '#0068ff', borderRadius: '3px' }} />
                            </Box>
                        </Box>
                    ))}
                </Box>
            </Box>
            {/* Tasks */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Nhiệm vụ cần xử lý</Text>
                    <Text className="see-all" style={{ color: '#0068ff', fontSize: '14px' }}>Xem tất cả</Text>
                </Box>
                <List className="tasks-list" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {tasks.map((item, index) => (
                        <Box key={index} className="task-item" style={{ display: 'flex', alignItems: 'center', backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                            <Box className="task-icon" style={{ width: '36px', height: '36px', borderRadius: '50%', backgroundColor: '#fef4e6', color: '#ff9500', display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: '12px', fontSize: '18px' }}>
                                📝
                            </Box>
                            <Box className="task-info" flex="1">
                                <Text className="task-title" bold>{item.title}</Text>
                                <Text className="task-meta" style={{ fontSize: '12px', color: '#888' }}>{item.meta}</Text>
                            </Box>
                            <Box className="task-count" style={{ backgroundColor: '#0068ff', color: 'white', fontSize: '12px', fontWeight: 'bold', minWidth: '24px', height: '24px', borderRadius: '12px', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '0 8px' }}>
                                {item.count}
                            </Box>
                        </Box>
                    ))}
                </List>
            </Box>
            {/* Student Summary */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Text className="section-title" bold size="large" style={{ marginBottom: '15px' }}>Tổng quan học sinh</Text>
                <Box className="teacher-details" style={{ display: 'flex', justifyContent: 'space-between', backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                    {[
                        { value: '165', label: 'Tổng số' },
                        { value: '157', label: 'Đi học' },
                        { value: '8', label: 'Vắng' },
                        { value: '95%', label: 'Tỷ lệ' },
                    ].map((item, index) => (
                        <Box key={index} className="summary-item" flex flexDirection="column" alignItems="center" style={{ textAlign: 'center' }}>
                            <Text className="summary-value" bold style={{ fontSize: '20px', color: '#0068ff' }}>{item.value}</Text>
                            <Text className="summary-label" style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>{item.label}</Text>
                        </Box>
                    ))}
                </Box>
            </Box>
            {/* Calendar */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Lịch dạy trong tuần</Text>
                    <Text className="see-all" style={{ color: '#0068ff', fontSize: '14px' }}>Tháng</Text>
                </Box>
                <Box className="calendar-view" flex style={{ overflowX: 'auto', gap: '10px', paddingBottom: '10px' }}>
                    {[
                        { day: 'T2', number: '22', hasClass: true },
                        { day: 'T3', number: '23', today: true, hasClass: true },
                        { day: 'T4', number: '24', hasClass: true },
                        { day: 'T5', number: '25', hasClass: true },
                        { day: 'T6', number: '26' },
                        { day: 'T7', number: '27' },
                        { day: 'CN', number: '28' },
                    ].map((item, index) => (
                        <Box key={index} className={`calendar-day ${item.today ? 'today' : ''} ${item.hasClass ? 'has-class' : ''}`} flex flexDirection="column" alignItems="center" style={{ padding: '10px', minWidth: '60px' }}>
                            <Text className="day-name" style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>{item.day}</Text>
                            <Box className="day-number" style={{ width: '36px', height: '36px', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 'bold', backgroundColor: item.today ? '#0068ff' : 'transparent', color: item.today ? 'white' : 'black', border: item.hasClass ? '2px solid #0068ff' : 'none' }}>
                                {item.number}
                            </Box>
                            {item.hasClass && <Box className="day-indicator" style={{ width: '6px', height: '6px', borderRadius: '50%', backgroundColor: '#ff9500', marginTop: '5px' }} />}
                        </Box>
                    ))}
                </Box>
            </Box>
            {/* Notifications */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Thông báo gần đây</Text>
                    <Text className="see-all" style={{ color: '#0068ff', fontSize: '14px' }}>Xem tất cả</Text>
                </Box>
                <List className="notification-list" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {notifications.map((item, index) => (
                        <Box key={index} className="notification-item" style={{ display: 'flex', backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                            <Box className="notification-icon" style={{ width: '40px', height: '40px', borderRadius: '50%', backgroundColor: '#e8f0fe', color: '#0068ff', display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: '12px', fontSize: '20px' }}>
                                {item.icon}
                            </Box>
                            <Box className="notification-content" flex="1">
                                <Text className="notification-text" dangerouslySetInnerHTML={{ __html: item.text }} style={{ lineHeight: 1.4, marginBottom: '5px' }} />
                                <Text className="notification-time" style={{ fontSize: '12px', color: '#888' }}>{item.time}</Text>
                            </Box>
                        </Box>
                    ))}
                </List>
            </Box>
            {/* Modal tạo thông báo */}
            <Modal
                visible={modalVisible}
                title="Tạo thông báo mới"
                onClose={() => {
                    setModalVisible(false);
                    setError('');
                    setFormData({
                        type: 'teacher_to_student',
                        content: '',
                        recipients: { classId: '', department: '' },
                        zaloConfig: { enabled: false, groupId: '', groupName: '', scheduledTime: '' },
                    });
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { text: submitting ? 'Đang gửi...' : (formData.zaloConfig.scheduledTime ? 'Lên lịch' : 'Gửi ngay'), close: false, onClick: handleSubmit, disabled: submitting },
                ]}
            >
                <Box p={4}>
                    {error && <Text style={{ color: 'red', marginBottom: '10px' }}>{error}</Text>}

                    {/* Debug info */}
                    {process.env.NODE_ENV === 'development' && (
                        <Box style={{ marginBottom: '10px', padding: '10px', backgroundColor: '#f0f0f0', fontSize: '12px' }}>
                            <Text>Debug Info:</Text>
                            <Text>announcementConfig: {JSON.stringify(announcementConfig)}</Text>
                            <Text>availableClasses: {availableClasses.length} items</Text>
                            <Text>zaloGroups: {zaloGroups.length} items</Text>
                            <Text>formData.type: {formData.type}</Text>
                        </Box>
                    )}

                    {/* Loại thông báo */}
                    <Text style={{ marginBottom: '5px' }}>Loại thông báo</Text>
                    {loadingConfig ? (
                        <Box style={{ display: 'flex', justifyContent: 'center', padding: '10px' }}>
                            <LoadingIndicator />
                        </Box>
                    ) : (
                        <Select
                            placeholder="Chọn loại thông báo"
                            value={formData.type}
                            onChange={(value) => {
                                console.log('Type selection changed:', value);
                                handleFormChange('type', value);
                            }}
                            style={{ marginBottom: '15px' }}
                        >
                            {announcementConfig ? (
                                Object.entries(announcementConfig.typeLabels || {}).map(([key, label]) => (
                                    <Option key={key} value={key} title={label} />
                                ))
                            ) : (
                                <>
                                    <Option value="teacher_to_student" title="Giáo viên gửi học sinh" />
                                    <Option value="head_to_teacher" title="Tổ trưởng gửi giáo viên" />
                                    <Option value="principal_to_teacher" title="Hiệu trưởng gửi giáo viên" />
                                    <Option value="admin_to_all" title="Ban giám hiệu gửi toàn trường" />
                                </>
                            )}
                        </Select>
                    )}

                    {/* Chọn lớp học - chỉ hiển thị khi type là teacher_to_student */}
                    {formData.type === 'teacher_to_student' && (
                        <>
                            <Text style={{ marginBottom: '5px' }}>Chọn lớp học</Text>
                            {loadingClasses ? (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '10px' }}>
                                    <LoadingIndicator />
                                </Box>
                            ) : (
                                <Select
                                    placeholder="Chọn lớp học"
                                    value={formData.recipients.classId}
                                    onChange={(value) => {
                                        console.log('Class selection changed:', value);
                                        handleFormChange('classId', value);
                                    }}
                                    style={{ marginBottom: '15px' }}
                                >
                                    {availableClasses.length > 0 ? (
                                        availableClasses.map((cls) => (
                                            <Option key={cls.id} value={cls.id} title={`${cls.name} - ${cls.classRoom}`} />
                                        ))
                                    ) : (
                                        <Option value="10A1" title="10A1 - Toán học" />
                                    )}
                                </Select>
                            )}
                        </>
                    )}

                    {/* Chọn bộ môn - chỉ hiển thị khi type là head_to_teacher */}
                    {formData.type === 'head_to_teacher' && (
                        <>
                            <Text style={{ marginBottom: '5px' }}>Chọn bộ môn</Text>
                            <Select
                                placeholder="Chọn bộ môn"
                                value={formData.recipients.department}
                                onChange={(value) => {
                                    console.log('Department selection changed:', value);
                                    handleFormChange('department', value);
                                }}
                                style={{ marginBottom: '15px' }}
                            >
                                {announcementConfig && announcementConfig.departments ? (
                                    announcementConfig.departments.map((dept) => (
                                        <Option key={dept} value={dept} title={dept} />
                                    ))
                                ) : (
                                    <>
                                        <Option value="Toán" title="Toán" />
                                        <Option value="Văn" title="Văn" />
                                        <Option value="Anh" title="Anh" />
                                        <Option value="Lý" title="Lý" />
                                        <Option value="Hóa" title="Hóa" />
                                        <Option value="Sinh" title="Sinh" />
                                        <Option value="Sử" title="Sử" />
                                        <Option value="Địa" title="Địa" />
                                        <Option value="GDCD" title="GDCD" />
                                    </>
                                )}
                            </Select>
                        </>
                    )}
                    {/* Nội dung thông báo */}
                    <Text style={{ marginBottom: '5px' }}>Nội dung thông báo</Text>
                    <Input
                        type="textarea"
                        placeholder="Nhập nội dung thông báo"
                        value={formData.content}
                        onChange={(e) => handleFormChange('content', e.target.value)}
                        style={{ marginBottom: '15px', minHeight: '100px' }}
                    />
                    {/* Gửi qua Zalo */}
                    <Checkbox
                        checked={formData.zaloConfig.enabled}
                        onChange={(e) => handleFormChange('zaloEnabled', e.target.checked)}
                        style={{ marginBottom: '15px' }}
                    >
                        Gửi qua Zalo
                    </Checkbox>
                    {formData.zaloConfig.enabled && (
                        <>
                            {/* Chọn nhóm Zalo */}
                            <Text style={{ marginBottom: '5px' }}>Chọn nhóm Zalo</Text>
                            {loadingZaloGroups ? (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '10px' }}>
                                    <LoadingIndicator />
                                </Box>
                            ) : (
                                <Select
                                    placeholder="Chọn nhóm Zalo"
                                    value={formData.zaloConfig.groupId}
                                    onChange={(value) => {
                                        console.log('Zalo group selection changed:', value);
                                        handleFormChange('groupId', value);
                                    }}
                                    style={{ marginBottom: '15px' }}
                                >
                                    {zaloGroups.length > 0 ? (
                                        zaloGroups.map((group) => {
                                            const groupId = user.role === 'admin' ? group.group_id : group.groupId;
                                            const groupName = user.role === 'admin' ? group.name : group.groupName;
                                            return (
                                                <Option key={groupId} value={groupId} title={groupName} />
                                            );
                                        })
                                    ) : (
                                        <Option value="sample_group" title="Nhóm Zalo mẫu" />
                                    )}
                                </Select>
                            )}
                            {/* Chọn thời gian lên lịch */}
                            <Text style={{ marginBottom: '5px' }}>Thời gian gửi (tùy chọn)</Text>
                            <Input
                                type="datetime-local"
                                placeholder="Chọn thời gian gửi (tùy chọn)"
                                value={formData.zaloConfig.scheduledTime}
                                onChange={(e) => handleFormChange('scheduledTime', e.target.value)}
                                style={{ marginBottom: '15px' }}
                            />
                        </>
                    )}
                </Box>
            </Modal>
            {/* NewsComponent */}
            <NewsComponent />
            {/* Bottom Navigation */}
            <BottomNavigationEdu />
            {/* Floating Action Button */}
            <Button
                className="add-button"
                style={{
                    position: 'fixed',
                    bottom: '70px',
                    right: '20px',
                    width: '56px',
                    height: '56px',
                    borderRadius: '28px',
                    backgroundColor: '#0068ff',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '24px',
                    boxShadow: '0 2px 10px rgba(0, 104, 255, 0.3)',
                    zIndex: 100,
                }}
                onClick={() => setModalVisible(true)}
            >
                +
            </Button>
        </Box>
    );
};

export default TeacherEdu;