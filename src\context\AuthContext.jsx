import React, { createContext, useState, useEffect, useCallback, useRef } from 'react';
import { authApi } from '../utils/api';
import { parseJwt } from '../utils/jwt';

export const AuthContext = createContext();

// Utility function to navigate to login page
const navigateToLogin = () => {
    if (window.location.pathname !== '/login') {
        console.log('AuthContext: Navigating to login page');
        navigate('/login');
    }
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [classId, setClassId] = useState('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Dùng useRef để luôn có giá trị mới nhất khi callback được gọi
    const tokenRef = useRef(localStorage.getItem('token'));

    // Thêm getter/setter cho token để đảm bảo tokenRef luôn cập nhật
    const getToken = () => tokenRef.current;
    const setToken = (newToken) => {
        console.log('setToken: Updating token ref', newToken);
        tokenRef.current = newToken;
    };

    // Hàm lấy thông tin người dùng từ /api/auth/me
    const fetchUser = useCallback(async (retryCount = 0) => {
        const currentToken = getToken();
        console.log('fetchUser: Starting, pathname:', window.location.pathname, 'token:', currentToken);

        if (!currentToken) {
            console.log('fetchUser: No token, skipping');
            setLoading(false);
            setUser(null);
            setClassId('');
            navigateToLogin();
            return null;
        }

        const parsedToken = parseJwt(currentToken);
        if (!parsedToken || parsedToken.exp * 1000 < Date.now()) {
            console.log('fetchUser: Invalid or expired token');
            setLoading(false);
            setUser(null);
            setClassId('');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            setToken(null);
            navigateToLogin();
            return null;
        }

        try {
            setLoading(true);
            const response = await authApi.get('/auth/me', {
                headers: { Authorization: `Bearer ${currentToken}` }
            });
            const userData = response.data;
            console.log('fetchUser: User data received:', userData);

            setUser(userData);
            setClassId(userData.class?.id || '');
            localStorage.setItem('user', JSON.stringify(userData));
            setError(null);
            setLoading(false);

            return userData;
        } catch (err) {
            console.error('fetchUser error:', err);
            if (retryCount < 2) {
                console.log('fetchUser: Retrying, attempt:', retryCount + 1);
                return new Promise((resolve) => {
                    setTimeout(() => resolve(fetchUser(retryCount + 1)), 500);
                });
            }

            setError(err.response?.data?.msg || 'Lỗi khi lấy thông tin người dùng');
            setUser(null);
            setClassId('');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            setToken(null);

            navigateToLogin();

            setLoading(false);
            return null;
        }
    }, []);

    // Hàm cập nhật token - được cải tiến để đồng bộ cả localStorage và ref
    const updateToken = useCallback((newToken) => {
        console.log('updateToken: Setting new token:', newToken);

        // Cập nhật localStorage và ref
        if (newToken) {
            localStorage.setItem('token', newToken);
        } else {
            localStorage.removeItem('token');
        }

        // Cập nhật ref trước khi gọi fetchUser
        setToken(newToken);

        // Trả về Promise để có thể await
        return fetchUser();
    }, []);

    // Khởi tạo auth context khi component mount
    useEffect(() => {
        console.log('AuthContext: Initializing');
        const initAuth = async () => {
            const currentToken = localStorage.getItem('token');
            console.log('AuthContext: Initial token check:', currentToken);

            // Đảm bảo tokenRef được cập nhật
            setToken(currentToken);

            if (!currentToken) {
                console.log('AuthContext: No token found');
                setLoading(false);
                return;
            }

            // Kiểm tra token hợp lệ
            const parsedToken = parseJwt(currentToken);
            if (!parsedToken || parsedToken.exp * 1000 < Date.now()) {
                console.log('AuthContext: Invalid or expired token on init');
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                setToken(null);
                setLoading(false);
                return;
            }

            // Sử dụng user cache trước
            const cachedUser = localStorage.getItem('user');
            if (cachedUser) {
                console.log('AuthContext: Using cached user');
                const userData = JSON.parse(cachedUser);
                setUser(userData);
                setClassId(userData.class?.id || '');
            }

            // Vẫn gọi API để đảm bảo dữ liệu mới nhất
            await fetchUser();
        };

        initAuth();
    }, []);

    // Lắng nghe thay đổi localStorage
    useEffect(() => {
        const handleStorageChange = (e) => {
            if (e.key === 'token') {
                console.log('AuthContext: Storage token changed:', e.newValue);

                setToken(e.newValue || null);

                // Nếu token bị xóa trong tab khác
                if (!e.newValue && user) {
                    setUser(null);
                    setClassId('');
                    localStorage.removeItem('user');
                }
            }
        };

        window.addEventListener('storage', handleStorageChange);
        return () => window.removeEventListener('storage', handleStorageChange);
    }, [user]);

    // Hàm logout
    const logout = useCallback(() => {
        console.log('AuthContext: Logging out');
        setUser(null);
        setClassId('');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        setToken(null);

        navigateToLogin();
    }, []);

    // Giá trị context
    const value = {
        user,
        classId,
        loading,
        error,
        fetchUser,
        updateToken,
        logout,
        // Thêm getToken để components có thể luôn lấy token mới nhất
        getToken
    };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};