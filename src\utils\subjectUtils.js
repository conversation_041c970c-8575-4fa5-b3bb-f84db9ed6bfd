// src/utils/subjectUtils.js

/**
 * Lấy icon tương ứng với tên môn học
 * @param {string} subjectName - Tên môn học
 * @returns {string} - Icon emoji tương ứng
 */
export const getSubjectIcon = (subjectName) => {
    const icons = {
        'Toán học': '📐',
        'Vật lý': '⚡',
        'Hóa học': '⚗️',
        'Địa lý': '🌍',
        'Lịch sử': '📜',
        'Ngữ văn': '📝',
        'Tiếng anh': '🇬🇧',
        'Sinh học': '🧬',
        'Tin học': '💻',
        'Công nghệ': '🔧',
        'Giáo dục công dân': '⚖️',
        'Thể dục': '🏃',
        'Âm nhạc': '🎵',
        'M<PERSON> thuật': '🎨',
    };
    return icons[subjectName] || '📚';
};

/**
 * Lấy class CSS cho icon môn học
 * @param {string} subjectName - Tên môn họ<PERSON>
 * @returns {string} - Tên class CSS
 */
export const getSubjectIconClass = (subjectName) => {
    const classes = {
        'Toán học': 'icon-math',
        'Vật lý': 'icon-physics',
        'Hóa học': 'icon-chemistry',
        'Ngữ văn': 'icon-literature',
        'Tiếng Anh': 'icon-english',
        'Sinh học': 'icon-biology',
        'Tin học': 'icon-informatics',
        'Công nghệ': 'icon-technology',
        'Địa lý': 'icon-geography',
        'Lịch sử': 'icon-history',
        'Giáo dục công dân': 'icon-civics',
    };
    return classes[subjectName] || 'icon-default';
};
