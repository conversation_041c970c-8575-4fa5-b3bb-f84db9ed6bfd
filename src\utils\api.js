import axios from 'axios';
import config from '../config/config';

// Instance cho Authorization: Bearer
const api = axios.create({
  baseURL: config.apiBaseUrl,
});

api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  return config;
});

// Instance cho x-auth-token
const authApi = axios.create({
  baseURL: config.apiBaseUrl,
});

authApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers['x-auth-token'] = token;
  }
  return config;
});

export { api, authApi };