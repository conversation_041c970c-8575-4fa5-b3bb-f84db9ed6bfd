import React from 'react';
import { Box, Spinner } from 'zmp-ui';

const Loading = () => {
  return (
    <Box
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.3)', // Overlay trong suốt
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000, // <PERSON><PERSON><PERSON> bảo overlay ở trên cùng
      }}
    >
      <Spinner visible />
    </Box>
  );
};

export default Loading;