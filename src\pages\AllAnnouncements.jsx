import React, { useEffect, useState, useRef, useContext } from 'react';
import { Box, Text, List, useNavigate } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import BottomNavigation from '../components/BottomNavigationEdu';
import Loading from '../components/Loading';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import HeaderSpacer from '../components/HeaderSpacer';

const AllAnnouncements = () => {
    const navigate = useNavigate();
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [announcements, setAnnouncements] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const observerRef = useRef(null);

    // Kiểm tra user và redirect nếu chưa đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Lấy danh sách thông báo
    useEffect(() => {
        if (classId) {
            setLoading(true);
            authApi
                .get(`/announcements?classId=${classId}&page=${page}&limit=10`)
                .then((response) => {
                    setAnnouncements((prev) => [...prev, ...response.data.announcements]);
                    setTotalPages(response.data.totalPages);
                    setLoading(false);
                })
                .catch((err) => {
                    console.log('Error fetching announcements:', err);
                    setLoading(false);
                });
        }
    }, [classId, page]);

    // Infinite scroll
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && page < totalPages && !loading) {
                    setPage((prev) => prev + 1);
                }
            },
            { threshold: 0.1 }
        );

        if (observerRef.current) {
            observer.observe(observerRef.current);
        }

        return () => {
            if (observerRef.current) {
                observer.unobserve(observerRef.current);
            }
        };
    }, [page, totalPages, loading]);

    return (
        <Box style={{ backgroundColor: '#f5f5f5', minHeight: '100vh', position: 'relative' }}>
            <HeaderEdu />
            <HeaderSpacer />
            {authLoading ? (
                <Text>Đang tải thông tin...</Text>
            ) : (
                <Box style={{ padding: '15px' }}>
                    <Text bold size="xLarge" style={{ marginBottom: '15px' }}>
                        Tất cả thông báo
                    </Text>
                    <List style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        {announcements.length > 0 ? (
                            announcements.map((item) => (
                                <Box
                                    key={item._id}
                                    style={{
                                        backgroundColor: '#f9f9f9',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                    }}
                                >
                                    <Box flex alignItems="center" style={{ marginBottom: '8px' }}>
                                        <Box
                                            style={{
                                                width: '36px',
                                                height: '36px',
                                                borderRadius: '50%',
                                                backgroundColor: '#e0e0e0',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                marginRight: '10px',
                                            }}
                                        >
                                            👨‍🏫
                                        </Box>
                                        <Box>
                                            <Text bold>{item.teacher.name}</Text>
                                            <Text style={{ fontSize: '12px', color: '#888' }}>
                                                {item.subject} •{' '}
                                                {formatDistanceToNow(new Date(item.createdAt), { locale: vi, addSuffix: true })}
                                            </Text>
                                        </Box>
                                    </Box>
                                    <Text style={{ fontSize: '14px', lineHeight: 1.4 }}>{item.content}</Text>
                                </Box>
                            ))
                        ) : (
                            <Text>Chưa có thông báo</Text>
                        )}
                    </List>
                    {loading && <Loading />}
                    <div ref={observerRef} style={{ height: '20px' }} />
                </Box>
            )}
            <BottomNavigation />
        </Box>
    );
};

export default AllAnnouncements;