import React, { useEffect, useState, useContext } from 'react';
import { Box, Text, useNavigate } from 'zmp-ui';
import { useParams } from 'react-router-dom';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import LoadingIndicator from '../components/LoadingIndicator';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import useSwipeNavigation from '../hooks/useSwipeNavigation';

const NewsDetail = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [newsDetail, setNewsDetail] = useState(null);
    const [loading, setLoading] = useState(true);

    // Kiểm tra user và redirect nếu chưa đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Lấy chi tiết tin tức
    useEffect(() => {
        if (id) {
            setLoading(true);
            authApi
                .get(`/news/${id}`)
                .then((response) => {
                    setNewsDetail(response.data);
                    setLoading(false);
                })
                .catch((err) => {
                    console.log('Error fetching news detail:', err);
                    setLoading(false);
                });
        }
    }, [id]);

    // Format date
    const formatNewsDate = (dateString) => {
        try {
            return formatDistanceToNow(new Date(dateString), { locale: vi, addSuffix: true });
        } catch (error) {
            return 'Không xác định';
        }
    };

    // Sử dụng hook useSwipeNavigation để hỗ trợ vuốt để quay lại
    useSwipeNavigation({
        threshold: 80,
        minVelocity: 0.3,
        swipeDirection: "right",
        preventDefaultOnSwipe: true
    });

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu onBackClick={() => navigate(-1)} showBackButton={true} />
            <HeaderSpacer />

            {loading || authLoading ? (
                <Box style={{ padding: '20px', display: 'flex', justifyContent: 'center' }}>
                    <LoadingIndicator />
                </Box>
            ) : newsDetail ? (
                <Box className="page-container" style={{ padding: '15px', flex: 1 }}>
                    <Text bold size="xLarge" style={{ marginBottom: '10px' }}>
                        {newsDetail.title}
                    </Text>

                    <Box style={{ display: 'flex', alignItems: 'center', marginBottom: '15px', justifyContent: 'space-between' }}>
                        <Text style={{ fontSize: '12px', color: '#666' }}>
                            {formatNewsDate(newsDetail.createdAt)} • {newsDetail.categoryLabel || 'Tin tức chung'}
                        </Text>
                        <Box style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                            <Text style={{ fontSize: '14px' }}>👁️</Text>
                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                {newsDetail.views || 0} lượt xem
                            </Text>
                        </Box>
                    </Box>

                    {newsDetail.image && (
                        <Box style={{ marginBottom: '15px' }}>
                            <img
                                src={newsDetail.image}
                                alt={newsDetail.title}
                                style={{
                                    width: '100%',
                                    borderRadius: '8px',
                                    maxHeight: '300px',
                                    objectFit: 'cover'
                                }}
                            />
                        </Box>
                    )}

                    <Box
                        style={{
                            backgroundColor: 'white',
                            padding: '15px',
                            borderRadius: '8px',
                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                        }}
                    >
                        <div
                            dangerouslySetInnerHTML={{ __html: newsDetail.content }}
                            style={{
                                fontSize: '14px',
                                lineHeight: 1.6,
                                color: '#333'
                            }}
                        />
                    </Box>
                </Box>
            ) : (
                <Box style={{ padding: '20px', textAlign: 'center' }}>
                    <Text>Không tìm thấy tin tức</Text>
                </Box>
            )}

            <BottomNavigationEdu />
        </Box>
    );
};

export default NewsDetail;
